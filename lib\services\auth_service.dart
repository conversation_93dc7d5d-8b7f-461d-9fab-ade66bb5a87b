import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import 'firebase_service.dart';

class AuthService {
  static final FirebaseAuth _auth = FirebaseService.auth;
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  // تسجيل الدخول كضيف (مؤقت)
  static Future<UserModel?> signInAsGuest() async {
    try {
      // إنشاء مستخدم مؤقت بدون مصادقة
      String guestId = 'guest_${DateTime.now().millisecondsSinceEpoch}';

      UserModel guestUser = UserModel(
        id: guestId,
        name: 'مستخدم ضيف',
        email: '<EMAIL>',
        avatar: '',
        isOnline: true,
        lastSeen: DateTime.now(),
        joinedAt: DateTime.now(),
        academicYear: 'غير محدد',
        department: 'غير محدد',
        isGuest: true,
      );

      // حفظ بيانات المستخدم الضيف محلياً
      await _saveUserToFirestore(guestUser);

      return guestUser;
    } catch (e) {
      print('Error signing in as guest: $e');
      return null;
    }
  }

  // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<UserModel?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        UserModel? user = await getUserById(result.user!.uid);
        if (user != null) {
          await updateUserOnlineStatus(user.id, true);
          return user;
        }
      }
      return null;
    } catch (e) {
      print('Error signing in: $e');
      return null;
    }
  }

  // إنشاء حساب جديد
  static Future<UserModel?> createUserWithEmailAndPassword(
    String email,
    String password,
    String name,
    String academicYear,
    String department,
  ) async {
    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        UserModel newUser = UserModel(
          id: result.user!.uid,
          name: name,
          email: email,
          avatar: '',
          isOnline: true,
          lastSeen: DateTime.now(),
          joinedAt: DateTime.now(),
          academicYear: academicYear,
          department: department,
          isGuest: false,
        );

        await _saveUserToFirestore(newUser);
        return newUser;
      }
      return null;
    } catch (e) {
      print('Error creating user: $e');
      return null;
    }
  }

  // تسجيل الخروج
  static Future<void> signOut() async {
    try {
      String? userId = getCurrentUserId();
      if (userId != null) {
        await updateUserOnlineStatus(userId, false);
      }
      await _auth.signOut();
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  // الحصول على المستخدم الحالي
  static String? getCurrentUserId() {
    return _auth.currentUser?.uid;
  }

  // الحصول على بيانات المستخدم من Firestore
  static Future<UserModel?> getUserById(String userId) async {
    try {
      DocumentSnapshot doc =
          await _firestore.collection('users').doc(userId).get();

      if (doc.exists) {
        return UserModel.fromJson(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      print('Error getting user: $e');
      return null;
    }
  }

  // حفظ بيانات المستخدم في Firestore
  static Future<void> _saveUserToFirestore(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.id).set(user.toJson());
    } catch (e) {
      print('Error saving user to Firestore: $e');
    }
  }

  // تحديث حالة الاتصال للمستخدم
  static Future<void> updateUserOnlineStatus(
    String userId,
    bool isOnline,
  ) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isOnline': isOnline,
        'lastSeen': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error updating user online status: $e');
    }
  }

  // تحديث بيانات المستخدم
  static Future<void> updateUserProfile(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.id).update(user.toJson());
    } catch (e) {
      print('Error updating user profile: $e');
    }
  }

  // الحصول على جميع المستخدمين
  static Future<List<UserModel>> getAllUsers() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection('users').get();

      return snapshot.docs
          .map((doc) => UserModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error getting all users: $e');
      return [];
    }
  }

  // البحث عن المستخدمين
  static Future<List<UserModel>> searchUsers(String query) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection('users')
              .where('name', isGreaterThanOrEqualTo: query)
              .where('name', isLessThan: query + 'z')
              .get();

      return snapshot.docs
          .map((doc) => UserModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error searching users: $e');
      return [];
    }
  }

  // التحقق من حالة المصادقة
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // إعادة تعيين كلمة المرور
  static Future<bool> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      print('Error resetting password: $e');
      return false;
    }
  }
}
