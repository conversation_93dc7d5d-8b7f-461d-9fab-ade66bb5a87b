import 'package:cloud_firestore/cloud_firestore.dart';

class Comment {
  final String id;
  final String postId;
  final String authorId;
  final String authorName;
  final String authorAvatar;
  final String content;
  final DateTime createdAt;
  final DateTime? editedAt;
  final List<String> likedBy;
  final String? parentCommentId; // للردود على التعليقات
  final List<String> replies; // معرفات الردود
  final bool isDeleted;
  final bool isAnonymous;

  Comment({
    required this.id,
    required this.postId,
    required this.authorId,
    required this.authorName,
    this.authorAvatar = '',
    required this.content,
    required this.createdAt,
    this.editedAt,
    this.likedBy = const [],
    this.parentCommentId,
    this.replies = const [],
    this.isDeleted = false,
    this.isAnonymous = false,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'] ?? '',
      postId: json['postId'] ?? '',
      authorId: json['authorId'] ?? '',
      authorName: json['authorName'] ?? '',
      authorAvatar: json['authorAvatar'] ?? '',
      content: json['content'] ?? '',
      createdAt: _parseDateTime(json['createdAt']) ?? DateTime.now(),
      editedAt: _parseDateTime(json['editedAt']),
      likedBy: List<String>.from(json['likedBy'] ?? []),
      parentCommentId: json['parentCommentId'],
      replies: List<String>.from(json['replies'] ?? []),
      isDeleted: json['isDeleted'] ?? false,
      isAnonymous: json['isAnonymous'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'authorId': authorId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'content': content,
      'createdAt': Timestamp.fromDate(createdAt),
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
      'likedBy': likedBy,
      'parentCommentId': parentCommentId,
      'replies': replies,
      'isDeleted': isDeleted,
      'isAnonymous': isAnonymous,
    };
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is Timestamp) return value.toDate();
    if (value is DateTime) return value;
    if (value is String) return DateTime.tryParse(value);
    return null;
  }

  Comment copyWith({
    String? id,
    String? postId,
    String? authorId,
    String? authorName,
    String? authorAvatar,
    String? content,
    DateTime? createdAt,
    DateTime? editedAt,
    List<String>? likedBy,
    String? parentCommentId,
    List<String>? replies,
    bool? isDeleted,
    bool? isAnonymous,
  }) {
    return Comment(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      editedAt: editedAt ?? this.editedAt,
      likedBy: likedBy ?? this.likedBy,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      replies: replies ?? this.replies,
      isDeleted: isDeleted ?? this.isDeleted,
      isAnonymous: isAnonymous ?? this.isAnonymous,
    );
  }

  bool isLikedBy(String userId) => likedBy.contains(userId);

  Comment toggleLike(String userId) {
    List<String> newLikedBy = List.from(likedBy);
    if (newLikedBy.contains(userId)) {
      newLikedBy.remove(userId);
    } else {
      newLikedBy.add(userId);
    }
    return copyWith(likedBy: newLikedBy);
  }

  Comment addReply(String replyId) {
    List<String> newReplies = List.from(replies);
    if (!newReplies.contains(replyId)) {
      newReplies.add(replyId);
    }
    return copyWith(replies: newReplies);
  }

  Comment removeReply(String replyId) {
    List<String> newReplies = List.from(replies);
    newReplies.remove(replyId);
    return copyWith(replies: newReplies);
  }

  bool get isReply => parentCommentId != null;
  bool get hasReplies => replies.isNotEmpty;

  String get displayAuthorName {
    if (isAnonymous) return 'مستخدم مجهول';
    return authorName.isEmpty ? 'مستخدم' : authorName;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  @override
  String toString() {
    return 'Comment(id: $id, postId: $postId, authorName: $authorName, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Comment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
