import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/chat.dart';
import '../models/user_model.dart';
import 'firebase_service.dart';
import 'auth_service.dart';

class ChatService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;
  static final Map<String, StreamSubscription> _messageSubscriptions = {};
  static final Map<String, StreamSubscription> _chatRoomSubscriptions = {};

  // إنشاء غرفة دردشة جديدة
  static Future<ChatRoom?> createChatRoom({
    required String name,
    required String description,
    required ChatRoomType type,
    String? yearId,
    List<String> initialMembers = const [],
  }) async {
    try {
      String chatRoomId = FirebaseService.generateId();
      String? currentUserId = AuthService.getCurrentUserId();
      
      if (currentUserId == null) return null;

      List<String> memberIds = List.from(initialMembers);
      if (!memberIds.contains(currentUserId)) {
        memberIds.add(currentUserId);
      }

      ChatRoom chatRoom = ChatRoom(
        id: chatRoomId,
        name: name,
        description: description,
        type: type,
        yearId: yearId,
        memberIds: memberIds,
        createdAt: DateTime.now(),
        lastActivity: DateTime.now(),
        isActive: true,
      );

      await _firestore
          .collection('chatRooms')
          .doc(chatRoomId)
          .set(chatRoom.toJson());

      return chatRoom;
    } catch (e) {
      print('Error creating chat room: $e');
      return null;
    }
  }

  // الحصول على جميع غرف الدردشة
  static Stream<List<ChatRoom>> getChatRooms() {
    return _firestore
        .collection('chatRooms')
        .where('isActive', isEqualTo: true)
        .orderBy('lastActivity', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => ChatRoom.fromJson(doc.data()))
          .toList();
    });
  }

  // الحصول على غرف الدردشة التي انضم إليها المستخدم
  static Stream<List<ChatRoom>> getUserChatRooms(String userId) {
    return _firestore
        .collection('chatRooms')
        .where('memberIds', arrayContains: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('lastActivity', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => ChatRoom.fromJson(doc.data()))
          .toList();
    });
  }

  // الانضمام إلى غرفة دردشة
  static Future<bool> joinChatRoom(String chatRoomId, String userId) async {
    try {
      await _firestore
          .collection('chatRooms')
          .doc(chatRoomId)
          .update({
        'memberIds': FieldValue.arrayUnion([userId]),
        'lastActivity': FieldValue.serverTimestamp(),
      });

      // إضافة رسالة نظام للانضمام
      await _sendSystemMessage(
        chatRoomId,
        'انضم مستخدم جديد إلى الدردشة',
      );

      return true;
    } catch (e) {
      print('Error joining chat room: $e');
      return false;
    }
  }

  // مغادرة غرفة دردشة
  static Future<bool> leaveChatRoom(String chatRoomId, String userId) async {
    try {
      await _firestore
          .collection('chatRooms')
          .doc(chatRoomId)
          .update({
        'memberIds': FieldValue.arrayRemove([userId]),
        'lastActivity': FieldValue.serverTimestamp(),
      });

      // إضافة رسالة نظام للمغادرة
      await _sendSystemMessage(
        chatRoomId,
        'غادر مستخدم الدردشة',
      );

      return true;
    } catch (e) {
      print('Error leaving chat room: $e');
      return false;
    }
  }

  // إرسال رسالة
  static Future<bool> sendMessage({
    required String chatRoomId,
    required String content,
    MessageType type = MessageType.text,
    List<String> attachments = const [],
    String? replyToId,
  }) async {
    try {
      String? currentUserId = AuthService.getCurrentUserId();
      if (currentUserId == null) return false;

      UserModel? currentUser = await AuthService.getUserById(currentUserId);
      if (currentUser == null) return false;

      String messageId = FirebaseService.generateId();

      ChatMessage message = ChatMessage(
        id: messageId,
        senderId: currentUserId,
        senderName: currentUser.name,
        senderAvatar: currentUser.avatar,
        content: content,
        timestamp: DateTime.now(),
        type: type,
        attachments: attachments,
        chatRoomId: chatRoomId,
        replyToId: replyToId,
      );

      // حفظ الرسالة
      await _firestore
          .collection('messages')
          .doc(messageId)
          .set(message.toJson());

      // تحديث آخر نشاط في غرفة الدردشة
      await _firestore
          .collection('chatRooms')
          .doc(chatRoomId)
          .update({
        'lastActivity': FieldValue.serverTimestamp(),
        'lastMessageId': messageId,
      });

      return true;
    } catch (e) {
      print('Error sending message: $e');
      return false;
    }
  }

  // الحصول على رسائل غرفة دردشة
  static Stream<List<ChatMessage>> getChatMessages(String chatRoomId) {
    return _firestore
        .collection('messages')
        .where('chatRoomId', isEqualTo: chatRoomId)
        .where('isDeleted', isEqualTo: false)
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => ChatMessage.fromJson(doc.data()))
          .toList();
    });
  }

  // إرسال رسالة نظام
  static Future<void> _sendSystemMessage(String chatRoomId, String content) async {
    try {
      String messageId = FirebaseService.generateId();

      ChatMessage systemMessage = ChatMessage(
        id: messageId,
        senderId: 'system',
        senderName: 'النظام',
        senderAvatar: '',
        content: content,
        timestamp: DateTime.now(),
        type: MessageType.system,
        chatRoomId: chatRoomId,
      );

      await _firestore
          .collection('messages')
          .doc(messageId)
          .set(systemMessage.toJson());
    } catch (e) {
      print('Error sending system message: $e');
    }
  }

  // تحديث حالة قراءة الرسالة
  static Future<void> markMessageAsRead(String messageId, String userId) async {
    try {
      await _firestore
          .collection('messages')
          .doc(messageId)
          .update({
        'readBy': FieldValue.arrayUnion([userId]),
      });
    } catch (e) {
      print('Error marking message as read: $e');
    }
  }

  // حذف رسالة
  static Future<bool> deleteMessage(String messageId, String userId) async {
    try {
      DocumentSnapshot messageDoc = await _firestore
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) return false;

      ChatMessage message = ChatMessage.fromJson(
        messageDoc.data() as Map<String, dynamic>
      );

      // التحقق من أن المستخدم هو مرسل الرسالة
      if (message.senderId != userId) return false;

      await _firestore
          .collection('messages')
          .doc(messageId)
          .update({
        'isDeleted': true,
        'content': 'تم حذف هذه الرسالة',
      });

      return true;
    } catch (e) {
      print('Error deleting message: $e');
      return false;
    }
  }

  // البحث في الرسائل
  static Future<List<ChatMessage>> searchMessages(
    String chatRoomId, 
    String query
  ) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('messages')
          .where('chatRoomId', isEqualTo: chatRoomId)
          .where('isDeleted', isEqualTo: false)
          .get();

      List<ChatMessage> messages = snapshot.docs
          .map((doc) => ChatMessage.fromJson(doc.data() as Map<String, dynamic>))
          .where((message) => 
            message.content.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return messages;
    } catch (e) {
      print('Error searching messages: $e');
      return [];
    }
  }

  // تنظيف الاشتراكات
  static void dispose() {
    for (var subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    for (var subscription in _chatRoomSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();
    _chatRoomSubscriptions.clear();
  }
}
