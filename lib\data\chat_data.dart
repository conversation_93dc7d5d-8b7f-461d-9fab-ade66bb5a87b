import '../models/chat.dart';

class ChatData {
  static final ChatUser currentUser = ChatUser(
    id: 'user_1',
    name: 'أحمد محمد',
    email: '<EMAIL>',
    avatar: '',
    yearId: 'year1',
    role: UserRole.student,
    isOnline: true,
    lastSeen: DateTime.now(),
  );

  static List<ChatUser> getAllUsers() {
    return [
      currentUser,
      ChatUser(
        id: 'user_2',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        avatar: '',
        yearId: 'year1',
        role: UserRole.student,
        isOnline: true,
        lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      Chat<PERSON>ser(
        id: 'user_3',
        name: 'محمد حسن',
        email: '<EMAIL>',
        avatar: '',
        yearId: 'year2',
        role: UserRole.student,
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ChatUser(
        id: 'user_4',
        name: 'د. عبد الرحمن',
        email: 'dr.a<PERSON><PERSON><PERSON><PERSON><PERSON>@example.com',
        avatar: '',
        role: UserRole.teacher,
        isOnline: true,
        lastSeen: DateTime.now(),
      ),
      ChatUser(
        id: 'user_5',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        avatar: '',
        yearId: 'year3',
        role: UserRole.student,
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
    ];
  }

  static List<ChatRoom> getChatRooms() {
    final now = DateTime.now();

    return [
      // الدردشة العامة
      ChatRoom(
        id: 'general',
        name: 'الدردشة العامة',
        description: 'دردشة عامة لجميع طلاب الكلية',
        type: ChatRoomType.general,
        memberIds: [], // الانضمام اختياري
        createdAt: now.subtract(const Duration(days: 30)),
        lastActivity: now.subtract(const Duration(minutes: 5)),
        messages: [], // سيتم تحميل الرسائل من Firebase
        unreadCount: 2,
      ),

      // دردشة الفرقة الأولى
      ChatRoom(
        id: 'year1_chat',
        name: 'دردشة الفرقة الأولى',
        description: 'دردشة خاصة بطلاب الفرقة الأولى',
        type: ChatRoomType.year,
        yearId: 'year1',
        memberIds:
            getAllUsers()
                .where((user) => user.yearId == 'year1')
                .map((user) => user.id)
                .toList(),
        createdAt: now.subtract(const Duration(days: 20)),
        lastActivity: now.subtract(const Duration(minutes: 10)),
        messages: [], // سيتم تحميل الرسائل من Firebase
        unreadCount: 1,
      ),

      // دردشة الفرقة الثانية
      ChatRoom(
        id: 'year2_chat',
        name: 'دردشة الفرقة الثانية',
        description: 'دردشة خاصة بطلاب الفرقة الثانية',
        type: ChatRoomType.year,
        yearId: 'year2',
        memberIds: [], // الانضمام اختياري
        createdAt: now.subtract(const Duration(days: 25)),
        lastActivity: now.subtract(const Duration(hours: 3)),
        messages: [], // سيتم تحميل الرسائل من Firebase
        unreadCount: 0,
      ),

      // دردشة الفرقة الثالثة
      ChatRoom(
        id: 'year3_chat',
        name: 'دردشة الفرقة الثالثة',
        description: 'دردشة خاصة بطلاب الفرقة الثالثة',
        type: ChatRoomType.year,
        yearId: 'year3',
        memberIds: [], // الانضمام اختياري
        createdAt: now.subtract(const Duration(days: 15)),
        lastActivity: now.subtract(const Duration(hours: 1)),
        messages: [], // سيتم تحميل الرسائل من Firebase
        unreadCount: 1,
      ),

      // دردشة الفرقة الرابعة
      ChatRoom(
        id: 'year4_chat',
        name: 'دردشة الفرقة الرابعة',
        description: 'دردشة خاصة بطلاب الفرقة الرابعة',
        type: ChatRoomType.year,
        yearId: 'year4',
        memberIds: [],
        createdAt: now.subtract(const Duration(days: 10)),
        lastActivity: now.subtract(const Duration(days: 1)),
        messages: [],
        unreadCount: 0,
      ),
    ];
  }

  static ChatRoom? getChatRoomById(String id) {
    try {
      return getChatRooms().firstWhere((room) => room.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<ChatRoom> getChatRoomsForUser(String userId) {
    return getChatRooms()
        .where((room) => room.memberIds.contains(userId))
        .toList();
  }

  static int getTotalUnreadCount() {
    return getChatRooms().fold(0, (total, room) => total + room.unreadCount);
  }

  // قائمة الدردشات المنضم إليها (محفوظة في الذاكرة)
  static final Set<String> _joinedRoomIds = <String>{};

  static bool isUserJoined(String roomId) {
    return _joinedRoomIds.contains(roomId);
  }

  static void joinRoom(String roomId) {
    _joinedRoomIds.add(roomId);
  }

  static void leaveRoom(String roomId) {
    _joinedRoomIds.remove(roomId);
  }

  static List<ChatRoom> getJoinedRooms() {
    return getChatRooms()
        .where((room) => _joinedRoomIds.contains(room.id))
        .toList();
  }

  static List<ChatRoom> getAvailableRooms() {
    return getChatRooms()
        .where((room) => !_joinedRoomIds.contains(room.id))
        .toList();
  }
}
