import 'package:flutter/material.dart';
import 'package:animations/animations.dart';
import '../data/academic_data.dart';
import '../models/subject.dart';
import '../theme/app_theme.dart';
import '../widgets/year_card.dart';
import '../widgets/animated_header.dart';
import 'subjects_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _headerAnimationController;
  late AnimationController _cardsAnimationController;
  late Animation<double> _headerAnimation;
  late Animation<double> _cardsAnimation;

  List<AcademicYear> academicYears = [];

  @override
  void initState() {
    super.initState();
    academicYears = AcademicData.getAcademicYears();

    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _headerAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: Curves.easeOutBack,
      ),
    );

    _cardsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _cardsAnimationController,
        curve: Curves.easeOutQuart,
      ),
    );

    // بدء الأنيميشن
    _headerAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _cardsAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _cardsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // الهيدر المتحرك
            SliverToBoxAdapter(
              child: AnimatedBuilder(
                animation: _headerAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      0,
                      50 * (1 - _headerAnimation.value.clamp(0.0, 1.0)),
                    ),
                    child: Opacity(
                      opacity: _headerAnimation.value.clamp(0.0, 1.0),
                      child: const AnimatedHeader(),
                    ),
                  );
                },
              ),
            ),

            // قائمة الفرق الدراسية
            SliverPadding(
              padding: const EdgeInsets.all(20),
              sliver: AnimatedBuilder(
                animation: _cardsAnimation,
                builder: (context, child) {
                  return SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final year = academicYears[index];
                      final delay = index * 0.1;
                      final animationValue = Curves.easeOutQuart.transform(
                        (_cardsAnimation.value - delay).clamp(0.0, 1.0),
                      );

                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - animationValue)),
                        child: Opacity(
                          opacity: animationValue,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: OpenContainer(
                              transitionType:
                                  ContainerTransitionType.fadeThrough,
                              transitionDuration: const Duration(
                                milliseconds: 500,
                              ),
                              openBuilder:
                                  (context, _) => SubjectsScreen(year: year),
                              closedBuilder:
                                  (context, openContainer) => YearCard(
                                    year: year,
                                    index: index,
                                    onTap: openContainer,
                                  ),
                              closedElevation: 0,
                              openElevation: 0,
                              closedShape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                        ),
                      );
                    }, childCount: academicYears.length),
                  );
                },
              ),
            ),

            // مساحة إضافية في الأسفل
            const SliverToBoxAdapter(child: SizedBox(height: 20)),
          ],
        ),
      ),
    );
  }
}
