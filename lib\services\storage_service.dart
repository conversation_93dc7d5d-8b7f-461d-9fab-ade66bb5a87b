import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../models/post_model.dart';
import 'firebase_service.dart';
import 'auth_service.dart';

class StorageService {
  static final FirebaseStorage _storage = FirebaseService.storage;

  // رفع صورة
  static Future<String?> uploadImage({
    required XFile imageFile,
    required String folder,
    String? customName,
  }) async {
    try {
      String? userId = AuthService.getCurrentUserId();
      if (userId == null) return null;

      String fileName =
          customName ??
          '${DateTime.now().millisecondsSinceEpoch}_${imageFile.name}';

      String path = '$folder/$userId/$fileName';

      Reference ref = _storage.ref().child(path);

      Uint8List imageData = await imageFile.readAsBytes();

      UploadTask uploadTask = ref.putData(
        imageData,
        SettableMetadata(
          contentType: 'image/${imageFile.name.split('.').last}',
          customMetadata: {
            'uploadedBy': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      print('Error uploading image: $e');
      return null;
    }
  }

  // رفع ملف
  static Future<PostFile?> uploadFile({
    required PlatformFile file,
    required String folder,
    String? customName,
  }) async {
    try {
      String? userId = AuthService.getCurrentUserId();
      if (userId == null) return null;

      String fileName =
          customName ?? '${DateTime.now().millisecondsSinceEpoch}_${file.name}';

      String path = '$folder/$userId/$fileName';

      Reference ref = _storage.ref().child(path);

      UploadTask uploadTask;

      if (file.bytes != null) {
        // للويب
        uploadTask = ref.putData(
          file.bytes!,
          SettableMetadata(
            contentType: _getContentType(file.extension ?? ''),
            customMetadata: {
              'uploadedBy': userId,
              'uploadedAt': DateTime.now().toIso8601String(),
              'originalName': file.name,
              'size': file.size.toString(),
            },
          ),
        );
      } else if (file.path != null) {
        // للموبايل
        File fileToUpload = File(file.path!);
        uploadTask = ref.putFile(
          fileToUpload,
          SettableMetadata(
            contentType: _getContentType(file.extension ?? ''),
            customMetadata: {
              'uploadedBy': userId,
              'uploadedAt': DateTime.now().toIso8601String(),
              'originalName': file.name,
              'size': file.size.toString(),
            },
          ),
        );
      } else {
        return null;
      }

      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();

      return PostFile(
        id: FirebaseService.generateId(),
        name: file.name,
        url: downloadUrl,
        type: file.extension ?? '',
        size: file.size,
        uploadedAt: DateTime.now(),
      );
    } catch (e) {
      print('Error uploading file: $e');
      return null;
    }
  }

  // رفع عدة صور
  static Future<List<String>> uploadMultipleImages({
    required List<XFile> imageFiles,
    required String folder,
  }) async {
    List<String> uploadedUrls = [];

    for (XFile imageFile in imageFiles) {
      String? url = await uploadImage(imageFile: imageFile, folder: folder);

      if (url != null) {
        uploadedUrls.add(url);
      }
    }

    return uploadedUrls;
  }

  // رفع عدة ملفات
  static Future<List<PostFile>> uploadMultipleFiles({
    required List<PlatformFile> files,
    required String folder,
  }) async {
    List<PostFile> uploadedFiles = [];

    for (PlatformFile file in files) {
      PostFile? uploadedFile = await uploadFile(file: file, folder: folder);

      if (uploadedFile != null) {
        uploadedFiles.add(uploadedFile);
      }
    }

    return uploadedFiles;
  }

  // حذف ملف
  static Future<bool> deleteFile(String fileUrl) async {
    try {
      Reference ref = _storage.refFromURL(fileUrl);
      await ref.delete();
      return true;
    } catch (e) {
      print('Error deleting file: $e');
      return false;
    }
  }

  // حذف عدة ملفات
  static Future<void> deleteMultipleFiles(List<String> fileUrls) async {
    for (String url in fileUrls) {
      await deleteFile(url);
    }
  }

  // الحصول على معلومات الملف
  static Future<FullMetadata?> getFileMetadata(String fileUrl) async {
    try {
      Reference ref = _storage.refFromURL(fileUrl);
      return await ref.getMetadata();
    } catch (e) {
      print('Error getting file metadata: $e');
      return null;
    }
  }

  // الحصول على حجم الملف
  static Future<int?> getFileSize(String fileUrl) async {
    try {
      FullMetadata? metadata = await getFileMetadata(fileUrl);
      return metadata?.size;
    } catch (e) {
      print('Error getting file size: $e');
      return null;
    }
  }

  // تحديد نوع المحتوى بناءً على امتداد الملف
  static String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'txt':
        return 'text/plain';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'mp4':
        return 'video/mp4';
      case 'mp3':
        return 'audio/mpeg';
      default:
        return 'application/octet-stream';
    }
  }

  // التحقق من صحة نوع الملف
  static bool isValidFileType(String extension, List<String> allowedTypes) {
    return allowedTypes.contains(extension.toLowerCase());
  }

  // التحقق من حجم الملف
  static bool isValidFileSize(int fileSize, int maxSizeInMB) {
    int maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return fileSize <= maxSizeInBytes;
  }

  // الحصول على رابط تحميل مؤقت
  static Future<String?> getTemporaryDownloadUrl(
    String fileUrl,
    Duration validity,
  ) async {
    try {
      Reference ref = _storage.refFromURL(fileUrl);
      return await ref.getDownloadURL();
    } catch (e) {
      print('Error getting temporary download URL: $e');
      return null;
    }
  }

  // ضغط الصورة قبل الرفع
  static Future<Uint8List?> compressImage(
    XFile imageFile, {
    int quality = 85,
  }) async {
    try {
      Uint8List imageData = await imageFile.readAsBytes();

      // هنا يمكن إضافة مكتبة ضغط الصور مثل image package
      // لكن للبساطة سنعيد البيانات كما هي
      return imageData;
    } catch (e) {
      print('Error compressing image: $e');
      return null;
    }
  }

  // إنشاء مجلد للمستخدم
  static String getUserFolder(String userId, String type) {
    return '$type/$userId';
  }

  // الحصول على مسار الملف في التخزين
  static String getStoragePath(String folder, String fileName) {
    String? userId = AuthService.getCurrentUserId();
    if (userId == null) return '';

    return '$folder/$userId/$fileName';
  }
}
