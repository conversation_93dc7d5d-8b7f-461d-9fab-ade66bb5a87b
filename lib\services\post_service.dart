import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/post_model.dart';
import '../models/comment_model.dart';
import '../models/user_model.dart';
import 'firebase_service.dart';
import 'auth_service.dart';

class PostService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  // إنشاء منشور جديد
  static Future<Post?> createPost({
    required String content,
    List<String> imageUrls = const [],
    List<PostFile> files = const [],
    Poll? poll,
    bool isAnonymous = false,
    String? academicYear,
    String? department,
  }) async {
    try {
      String? currentUserId = AuthService.getCurrentUserId();
      if (currentUserId == null) return null;

      UserModel? currentUser = await AuthService.getUserById(currentUserId);
      if (currentUser == null) return null;

      String postId = FirebaseService.generateId();

      Post post = Post(
        id: postId,
        authorId: currentUserId,
        authorName: isAnonymous ? 'مستخدم مجهول' : currentUser.name,
        authorAvatar: isAnonymous ? '' : currentUser.avatar,
        content: content,
        createdAt: DateTime.now(),
        imageUrls: imageUrls,
        files: files,
        poll: poll,
        isAnonymous: isAnonymous,
        academicYear: academicYear ?? currentUser.academicYear,
        department: department ?? currentUser.department,
      );

      await _firestore
          .collection('posts')
          .doc(postId)
          .set(post.toJson());

      return post;
    } catch (e) {
      print('Error creating post: $e');
      return null;
    }
  }

  // الحصول على جميع المنشورات
  static Stream<List<Post>> getPosts({
    String? academicYear,
    String? department,
    int limit = 20,
  }) {
    Query query = _firestore
        .collection('posts')
        .where('isDeleted', isEqualTo: false)
        .orderBy('createdAt', descending: true);

    if (academicYear != null) {
      query = query.where('academicYear', isEqualTo: academicYear);
    }

    if (department != null) {
      query = query.where('department', isEqualTo: department);
    }

    return query
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    });
  }

  // الحصول على منشورات مستخدم معين
  static Stream<List<Post>> getUserPosts(String userId) {
    return _firestore
        .collection('posts')
        .where('authorId', isEqualTo: userId)
        .where('isDeleted', isEqualTo: false)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Post.fromJson(doc.data()))
          .toList();
    });
  }

  // الحصول على منشور بالمعرف
  static Future<Post?> getPostById(String postId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection('posts')
          .doc(postId)
          .get();

      if (doc.exists) {
        return Post.fromJson(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      print('Error getting post: $e');
      return null;
    }
  }

  // تحديث منشور
  static Future<bool> updatePost(String postId, Map<String, dynamic> updates) async {
    try {
      await _firestore
          .collection('posts')
          .doc(postId)
          .update({
        ...updates,
        'editedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error updating post: $e');
      return false;
    }
  }

  // حذف منشور
  static Future<bool> deletePost(String postId, String userId) async {
    try {
      Post? post = await getPostById(postId);
      if (post == null || post.authorId != userId) return false;

      await _firestore
          .collection('posts')
          .doc(postId)
          .update({
        'isDeleted': true,
        'editedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error deleting post: $e');
      return false;
    }
  }

  // إعجاب/إلغاء إعجاب بمنشور
  static Future<bool> toggleLike(String postId, String userId) async {
    try {
      DocumentReference postRef = _firestore.collection('posts').doc(postId);
      
      return await _firestore.runTransaction((transaction) async {
        DocumentSnapshot postSnapshot = await transaction.get(postRef);
        
        if (!postSnapshot.exists) return false;
        
        Post post = Post.fromJson(postSnapshot.data() as Map<String, dynamic>);
        
        List<String> newLikedBy = List.from(post.likedBy);
        if (newLikedBy.contains(userId)) {
          newLikedBy.remove(userId);
        } else {
          newLikedBy.add(userId);
        }
        
        transaction.update(postRef, {'likedBy': newLikedBy});
        return true;
      });
    } catch (e) {
      print('Error toggling like: $e');
      return false;
    }
  }

  // مشاركة منشور
  static Future<bool> sharePost(String postId, String userId) async {
    try {
      await _firestore
          .collection('posts')
          .doc(postId)
          .update({
        'sharedBy': FieldValue.arrayUnion([userId]),
      });
      return true;
    } catch (e) {
      print('Error sharing post: $e');
      return false;
    }
  }

  // التصويت في استطلاع
  static Future<bool> voteInPoll(
    String postId, 
    String optionId, 
    String userId
  ) async {
    try {
      return await _firestore.runTransaction((transaction) async {
        DocumentReference postRef = _firestore.collection('posts').doc(postId);
        DocumentSnapshot postSnapshot = await transaction.get(postRef);
        
        if (!postSnapshot.exists) return false;
        
        Post post = Post.fromJson(postSnapshot.data() as Map<String, dynamic>);
        
        if (post.poll == null || post.poll!.hasUserVoted(userId)) return false;
        
        // إضافة المستخدم إلى قائمة المصوتين
        List<String> newVotedBy = List.from(post.poll!.votedBy);
        newVotedBy.add(userId);
        
        // إضافة الصوت للخيار المحدد
        List<PollOption> newOptions = post.poll!.options.map((option) {
          if (option.id == optionId) {
            List<String> newVotes = List.from(option.votes);
            newVotes.add(userId);
            return PollOption(
              id: option.id,
              text: option.text,
              votes: newVotes,
            );
          }
          return option;
        }).toList();
        
        Poll updatedPoll = Poll(
          id: post.poll!.id,
          question: post.poll!.question,
          options: newOptions,
          expiresAt: post.poll!.expiresAt,
          allowMultipleChoices: post.poll!.allowMultipleChoices,
          votedBy: newVotedBy,
        );
        
        transaction.update(postRef, {'poll': updatedPoll.toJson()});
        return true;
      });
    } catch (e) {
      print('Error voting in poll: $e');
      return false;
    }
  }

  // إضافة تعليق
  static Future<Comment?> addComment({
    required String postId,
    required String content,
    String? parentCommentId,
    bool isAnonymous = false,
  }) async {
    try {
      String? currentUserId = AuthService.getCurrentUserId();
      if (currentUserId == null) return null;

      UserModel? currentUser = await AuthService.getUserById(currentUserId);
      if (currentUser == null) return null;

      String commentId = FirebaseService.generateId();

      Comment comment = Comment(
        id: commentId,
        postId: postId,
        authorId: currentUserId,
        authorName: isAnonymous ? 'مستخدم مجهول' : currentUser.name,
        authorAvatar: isAnonymous ? '' : currentUser.avatar,
        content: content,
        createdAt: DateTime.now(),
        parentCommentId: parentCommentId,
        isAnonymous: isAnonymous,
      );

      await _firestore
          .collection('comments')
          .doc(commentId)
          .set(comment.toJson());

      // تحديث عدد التعليقات في المنشور
      await _firestore
          .collection('posts')
          .doc(postId)
          .update({
        'commentsCount': FieldValue.increment(1),
      });

      // إذا كان رد على تعليق، أضف معرف الرد للتعليق الأصلي
      if (parentCommentId != null) {
        await _firestore
            .collection('comments')
            .doc(parentCommentId)
            .update({
          'replies': FieldValue.arrayUnion([commentId]),
        });
      }

      return comment;
    } catch (e) {
      print('Error adding comment: $e');
      return null;
    }
  }

  // الحصول على تعليقات منشور
  static Stream<List<Comment>> getPostComments(String postId) {
    return _firestore
        .collection('comments')
        .where('postId', isEqualTo: postId)
        .where('isDeleted', isEqualTo: false)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Comment.fromJson(doc.data()))
          .toList();
    });
  }

  // البحث في المنشورات
  static Future<List<Post>> searchPosts(String query) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('posts')
          .where('isDeleted', isEqualTo: false)
          .get();

      List<Post> posts = snapshot.docs
          .map((doc) => Post.fromJson(doc.data() as Map<String, dynamic>))
          .where((post) => 
            post.content.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return posts;
    } catch (e) {
      print('Error searching posts: $e');
      return [];
    }
  }
}
