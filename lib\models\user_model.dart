import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String name;
  final String email;
  final String avatar;
  final bool isOnline;
  final DateTime lastSeen;
  final DateTime joinedAt;
  final String academicYear;
  final String department;
  final bool isGuest;
  final List<String> joinedChatRooms;
  final Map<String, dynamic> preferences;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.avatar = '',
    this.isOnline = false,
    required this.lastSeen,
    required this.joinedAt,
    this.academicYear = '',
    this.department = '',
    this.isGuest = false,
    this.joinedChatRooms = const [],
    this.preferences = const {},
  });

  // تحويل من JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      avatar: json['avatar'] ?? '',
      isOnline: json['isOnline'] ?? false,
      lastSeen: _parseDateTime(json['lastSeen']) ?? DateTime.now(),
      joinedAt: _parseDateTime(json['joinedAt']) ?? DateTime.now(),
      academicYear: json['academicYear'] ?? '',
      department: json['department'] ?? '',
      isGuest: json['isGuest'] ?? false,
      joinedChatRooms: List<String>.from(json['joinedChatRooms'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'isOnline': isOnline,
      'lastSeen': Timestamp.fromDate(lastSeen),
      'joinedAt': Timestamp.fromDate(joinedAt),
      'academicYear': academicYear,
      'department': department,
      'isGuest': isGuest,
      'joinedChatRooms': joinedChatRooms,
      'preferences': preferences,
    };
  }

  // نسخ مع تعديل
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? avatar,
    bool? isOnline,
    DateTime? lastSeen,
    DateTime? joinedAt,
    String? academicYear,
    String? department,
    bool? isGuest,
    List<String>? joinedChatRooms,
    Map<String, dynamic>? preferences,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      joinedAt: joinedAt ?? this.joinedAt,
      academicYear: academicYear ?? this.academicYear,
      department: department ?? this.department,
      isGuest: isGuest ?? this.isGuest,
      joinedChatRooms: joinedChatRooms ?? this.joinedChatRooms,
      preferences: preferences ?? this.preferences,
    );
  }

  // دالة مساعدة لتحويل التاريخ
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is Timestamp) return value.toDate();
    if (value is DateTime) return value;
    if (value is String) return DateTime.tryParse(value);
    return null;
  }

  // الحصول على الاسم المختصر للعرض
  String get displayName {
    if (name.isEmpty) return 'مستخدم';
    List<String> nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0]} ${nameParts[1]}';
    }
    return name;
  }

  // الحصول على الأحرف الأولى للاسم
  String get initials {
    if (name.isEmpty) return 'م';
    List<String> nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0][0]}${nameParts[1][0]}';
    }
    return name[0];
  }

  // التحقق من كون المستخدم متصل
  bool get isCurrentlyOnline {
    if (!isOnline) return false;
    
    // إذا كان آخر ظهور منذ أكثر من 5 دقائق، يعتبر غير متصل
    final difference = DateTime.now().difference(lastSeen);
    return difference.inMinutes < 5;
  }

  // الحصول على نص حالة الاتصال
  String get onlineStatusText {
    if (isCurrentlyOnline) return 'متصل الآن';
    
    final difference = DateTime.now().difference(lastSeen);
    if (difference.inMinutes < 60) {
      return 'آخر ظهور منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'آخر ظهور منذ ${difference.inHours} ساعة';
    } else {
      return 'آخر ظهور منذ ${difference.inDays} يوم';
    }
  }

  // التحقق من انضمام المستخدم لغرفة دردشة
  bool isJoinedToChatRoom(String chatRoomId) {
    return joinedChatRooms.contains(chatRoomId);
  }

  // إضافة غرفة دردشة للمستخدم
  UserModel joinChatRoom(String chatRoomId) {
    if (!joinedChatRooms.contains(chatRoomId)) {
      List<String> newJoinedRooms = List.from(joinedChatRooms);
      newJoinedRooms.add(chatRoomId);
      return copyWith(joinedChatRooms: newJoinedRooms);
    }
    return this;
  }

  // إزالة غرفة دردشة من المستخدم
  UserModel leaveChatRoom(String chatRoomId) {
    List<String> newJoinedRooms = List.from(joinedChatRooms);
    newJoinedRooms.remove(chatRoomId);
    return copyWith(joinedChatRooms: newJoinedRooms);
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, isOnline: $isOnline)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
