import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  static FirebaseMessaging get messaging => FirebaseMessaging.instance;

  static Future<void> initialize() async {
    try {
      // إعداد Firestore للعمل في وضع عدم الاتصال
      await firestore.enablePersistence();
      
      // إعداد إعدادات Firestore
      firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // إعداد Firebase Messaging
      await _setupMessaging();
      
      // إنشاء المجموعات الأساسية إذا لم تكن موجودة
      await _createInitialCollections();
      
      print('Firebase Service initialized successfully');
    } catch (e) {
      print('Error initializing Firebase Service: $e');
    }
  }

  static Future<void> _setupMessaging() async {
    try {
      // طلب إذن الإشعارات
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted permission for notifications');
        
        // الحصول على FCM token
        String? token = await messaging.getToken();
        print('FCM Token: $token');
      }
    } catch (e) {
      print('Error setting up messaging: $e');
    }
  }

  static Future<void> _createInitialCollections() async {
    try {
      // إنشاء مجموعة المستخدمين
      await firestore.collection('users').doc('init').set({
        'created': FieldValue.serverTimestamp(),
        'type': 'initialization'
      });

      // إنشاء مجموعة الدردشات
      await firestore.collection('chatRooms').doc('init').set({
        'created': FieldValue.serverTimestamp(),
        'type': 'initialization'
      });

      // إنشاء مجموعة المنشورات
      await firestore.collection('posts').doc('init').set({
        'created': FieldValue.serverTimestamp(),
        'type': 'initialization'
      });

      // إنشاء مجموعة الرسائل
      await firestore.collection('messages').doc('init').set({
        'created': FieldValue.serverTimestamp(),
        'type': 'initialization'
      });

      // إنشاء مجموعة التعليقات
      await firestore.collection('comments').doc('init').set({
        'created': FieldValue.serverTimestamp(),
        'type': 'initialization'
      });

      // حذف مستندات التهيئة
      await Future.delayed(const Duration(seconds: 1));
      await firestore.collection('users').doc('init').delete();
      await firestore.collection('chatRooms').doc('init').delete();
      await firestore.collection('posts').doc('init').delete();
      await firestore.collection('messages').doc('init').delete();
      await firestore.collection('comments').doc('init').delete();

      print('Initial collections created successfully');
    } catch (e) {
      print('Error creating initial collections: $e');
    }
  }

  // دوال مساعدة للتحقق من الاتصال
  static Future<bool> isConnected() async {
    try {
      await firestore.doc('test/connection').get();
      return true;
    } catch (e) {
      return false;
    }
  }

  // دالة للحصول على معرف المستخدم الحالي
  static String? getCurrentUserId() {
    return auth.currentUser?.uid;
  }

  // دالة للحصول على بيانات المستخدم الحالي
  static User? getCurrentUser() {
    return auth.currentUser;
  }

  // دالة لإنشاء معرف فريد
  static String generateId() {
    return firestore.collection('temp').doc().id;
  }

  // دالة للحصول على الوقت الحالي من الخادم
  static FieldValue getServerTimestamp() {
    return FieldValue.serverTimestamp();
  }

  // دالة لتحويل Timestamp إلى DateTime
  static DateTime? timestampToDateTime(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    if (timestamp is DateTime) {
      return timestamp;
    }
    return null;
  }

  // دالة لتحويل DateTime إلى Timestamp
  static Timestamp dateTimeToTimestamp(DateTime dateTime) {
    return Timestamp.fromDate(dateTime);
  }
}
