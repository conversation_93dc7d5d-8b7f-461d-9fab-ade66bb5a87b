import 'package:cloud_firestore/cloud_firestore.dart';

class ChatMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String senderAvatar;
  final String content;
  final DateTime timestamp;
  final MessageType type;
  final bool isRead;
  final String? replyToId;
  final List<String> attachments;
  final String chatRoomId;
  final List<String> readBy;
  final DateTime? editedAt;
  final bool isDeleted;

  ChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.senderAvatar,
    required this.content,
    required this.timestamp,
    this.type = MessageType.text,
    this.isRead = false,
    this.replyToId,
    this.attachments = const [],
    required this.chatRoomId,
    this.readBy = const [],
    this.editedAt,
    this.isDeleted = false,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] ?? '',
      senderId: json['senderId'] ?? '',
      senderName: json['senderName'] ?? '',
      senderAvatar: json['senderAvatar'] ?? '',
      content: json['content'] ?? '',
      timestamp: _parseDateTime(json['timestamp']) ?? DateTime.now(),
      type: MessageType.values[json['type'] ?? 0],
      isRead: json['isRead'] ?? false,
      replyToId: json['replyToId'],
      attachments: List<String>.from(json['attachments'] ?? []),
      chatRoomId: json['chatRoomId'] ?? '',
      readBy: List<String>.from(json['readBy'] ?? []),
      editedAt: _parseDateTime(json['editedAt']),
      isDeleted: json['isDeleted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'content': content,
      'timestamp': Timestamp.fromDate(timestamp),
      'type': type.index,
      'isRead': isRead,
      'replyToId': replyToId,
      'attachments': attachments,
      'chatRoomId': chatRoomId,
      'readBy': readBy,
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
      'isDeleted': isDeleted,
    };
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is Timestamp) return value.toDate();
    if (value is DateTime) return value;
    if (value is String) return DateTime.tryParse(value);
    return null;
  }

  ChatMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? content,
    DateTime? timestamp,
    MessageType? type,
    bool? isRead,
    String? replyToId,
    List<String>? attachments,
    String? chatRoomId,
    List<String>? readBy,
    DateTime? editedAt,
    bool? isDeleted,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      replyToId: replyToId ?? this.replyToId,
      attachments: attachments ?? this.attachments,
      chatRoomId: chatRoomId ?? this.chatRoomId,
      readBy: readBy ?? this.readBy,
      editedAt: editedAt ?? this.editedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}

enum MessageType { text, image, file, voice, system }

class ChatRoom {
  final String id;
  final String name;
  final String description;
  final ChatRoomType type;
  final String? yearId; // للدردشة الخاصة بالفرقة
  final List<String> memberIds;
  final List<ChatMessage> messages;
  final DateTime createdAt;
  final DateTime lastActivity;
  final String? lastMessageId;
  final int unreadCount;
  final bool isActive;

  ChatRoom({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    this.yearId,
    this.memberIds = const [],
    this.messages = const [],
    required this.createdAt,
    required this.lastActivity,
    this.lastMessageId,
    this.unreadCount = 0,
    this.isActive = true,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) {
    return ChatRoom(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      type: ChatRoomType.values[json['type'] ?? 0],
      yearId: json['yearId'],
      memberIds: List<String>.from(json['memberIds'] ?? []),
      messages: [], // الرسائل ستُحمل منفصلة من Firebase
      createdAt:
          ChatMessage._parseDateTime(json['createdAt']) ?? DateTime.now(),
      lastActivity:
          ChatMessage._parseDateTime(json['lastActivity']) ?? DateTime.now(),
      lastMessageId: json['lastMessageId'],
      unreadCount: json['unreadCount'] ?? 0,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'yearId': yearId,
      'memberIds': memberIds,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastActivity': Timestamp.fromDate(lastActivity),
      'lastMessageId': lastMessageId,
      'unreadCount': unreadCount,
      'isActive': isActive,
    };
  }

  ChatMessage? get lastMessage {
    if (messages.isEmpty) return null;
    return messages.last;
  }

  ChatRoom copyWith({
    String? id,
    String? name,
    String? description,
    ChatRoomType? type,
    String? yearId,
    List<String>? memberIds,
    List<ChatMessage>? messages,
    DateTime? createdAt,
    DateTime? lastActivity,
    String? lastMessageId,
    int? unreadCount,
    bool? isActive,
  }) {
    return ChatRoom(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      yearId: yearId ?? this.yearId,
      memberIds: memberIds ?? this.memberIds,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      lastActivity: lastActivity ?? this.lastActivity,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      unreadCount: unreadCount ?? this.unreadCount,
      isActive: isActive ?? this.isActive,
    );
  }
}

enum ChatRoomType {
  general, // الدردشة العامة
  year, // دردشة خاصة بالفرقة
  subject, // دردشة خاصة بالمادة
  private, // دردشة خاصة
}

class ChatUser {
  final String id;
  final String name;
  final String email;
  final String avatar;
  final String? yearId;
  final UserRole role;
  final bool isOnline;
  final DateTime lastSeen;

  ChatUser({
    required this.id,
    required this.name,
    required this.email,
    required this.avatar,
    this.yearId,
    this.role = UserRole.student,
    this.isOnline = false,
    required this.lastSeen,
  });

  factory ChatUser.fromJson(Map<String, dynamic> json) {
    return ChatUser(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      avatar: json['avatar'],
      yearId: json['yearId'],
      role: UserRole.values[json['role'] ?? 0],
      isOnline: json['isOnline'] ?? false,
      lastSeen: DateTime.parse(json['lastSeen']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'yearId': yearId,
      'role': role.index,
      'isOnline': isOnline,
      'lastSeen': lastSeen.toIso8601String(),
    };
  }
}

enum UserRole { student, teacher, admin }
